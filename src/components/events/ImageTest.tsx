import { FC, useState } from 'react';
import { getCompleteEventImageData } from '../../data/completeEventImageData';

export const ImageTest: FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const eventData = getCompleteEventImageData();

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testImageUrl1 = "/images/event_images/2023/AGM_2023_SACKOBA_Qatar/AGM_College_Day_Attendees_at_Tables.jpg";
  const testImageUrl2 = "/images/event_images/2023/SACKOBAQ_College_Day_Celebrations/Church_Service_Attendees_and_Thurible_Bearer.JPG";

  return (
    <div className="p-4 bg-blue-100 border border-blue-400 rounded">
      <h3 className="text-lg font-bold mb-4">Comprehensive Image Loading Test</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <p><strong>Event Data Status:</strong></p>
          <ul className="text-sm">
            <li>Total year groups: {eventData.length}</li>
            <li>Total events: {eventData.reduce((sum, year) => sum + year.events.length, 0)}</li>
            <li>Total images: {eventData.reduce((sum, year) => sum + year.totalImages, 0)}</li>
          </ul>
        </div>

        <div>
          <p><strong>Sample URLs:</strong></p>
          <ul className="text-xs">
            <li>URL 1: {testImageUrl1}</li>
            <li>URL 2: {testImageUrl2}</li>
          </ul>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div>
          <p><strong>Test Image 1 (.jpg):</strong></p>
          <img
            src={testImageUrl1}
            alt="Test Image 1"
            className="w-full h-24 object-cover border-2 border-gray-300"
            onLoad={() => addResult('✅ Image 1 (.jpg) loaded successfully')}
            onError={() => addResult('❌ Image 1 (.jpg) failed to load')}
          />
        </div>

        <div>
          <p><strong>Test Image 2 (.JPG):</strong></p>
          <img
            src={testImageUrl2}
            alt="Test Image 2"
            className="w-full h-24 object-cover border-2 border-gray-300"
            onLoad={() => addResult('✅ Image 2 (.JPG) loaded successfully')}
            onError={() => addResult('❌ Image 2 (.JPG) failed to load')}
          />
        </div>

        <div>
          <p><strong>First Event Image:</strong></p>
          {eventData.length > 0 && eventData[0].events.length > 0 && eventData[0].events[0].images.length > 0 ? (
            <img
              src={eventData[0].events[0].images[0].url}
              alt="First Event Image"
              className="w-full h-24 object-cover border-2 border-gray-300"
              onLoad={() => addResult('✅ First event image loaded successfully')}
              onError={() => addResult('❌ First event image failed to load')}
            />
          ) : (
            <div className="w-full h-24 bg-red-200 border-2 border-red-400 flex items-center justify-center text-xs">
              No event data
            </div>
          )}
        </div>
      </div>

      <div className="mb-4">
        <button
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 mr-2"
          onClick={async () => {
            try {
              addResult('🔍 Testing fetch for Image 1...');
              const response = await fetch(testImageUrl1);
              addResult(`📊 Fetch Image 1: ${response.status} ${response.statusText}`);

              if (response.ok) {
                const blob = await response.blob();
                addResult(`✅ Fetch Image 1 successful, size: ${blob.size} bytes`);
              }
            } catch (error) {
              addResult(`❌ Fetch Image 1 error: ${error}`);
            }
          }}
        >
          Test Fetch Image 1
        </button>

        <button
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          onClick={async () => {
            try {
              addResult('🔍 Testing fetch for Image 2...');
              const response = await fetch(testImageUrl2);
              addResult(`📊 Fetch Image 2: ${response.status} ${response.statusText}`);

              if (response.ok) {
                const blob = await response.blob();
                addResult(`✅ Fetch Image 2 successful, size: ${blob.size} bytes`);
              }
            } catch (error) {
              addResult(`❌ Fetch Image 2 error: ${error}`);
            }
          }}
        >
          Test Fetch Image 2
        </button>
      </div>

      <div>
        <p><strong>Test Results:</strong></p>
        <div className="bg-white p-2 rounded border max-h-40 overflow-y-auto">
          {testResults.length === 0 ? (
            <p className="text-gray-500 text-sm">No test results yet...</p>
          ) : (
            testResults.map((result, index) => (
              <div key={index} className="text-xs font-mono mb-1">
                {result}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};
