<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Test</title>
</head>
<body>
    <h1>Direct Image Test</h1>
    <p>Testing if images can be loaded directly:</p>
    
    <div style="margin: 20px 0;">
        <h3>Test Image 1:</h3>
        <img src="/images/event_images/2023/AGM_2023_SACKOBA_Qatar/AGM_College_Day_Attendees_at_Tables.jpg" 
             alt="Test Image 1" 
             style="width: 200px; height: 150px; object-fit: cover; border: 2px solid #ccc;"
             onload="console.log('✅ Image 1 loaded successfully')"
             onerror="console.error('❌ Image 1 failed to load'); this.style.border='2px solid red';">
    </div>
    
    <div style="margin: 20px 0;">
        <h3>Test Image 2:</h3>
        <img src="/images/event_images/2023/SACKOBAQ_College_Day_Celebrations/Church_Service_Attendees_and_Thurible_Bearer.JPG" 
             alt="Test Image 2" 
             style="width: 200px; height: 150px; object-fit: cover; border: 2px solid #ccc;"
             onload="console.log('✅ Image 2 loaded successfully')"
             onerror="console.error('❌ Image 2 failed to load'); this.style.border='2px solid red';">
    </div>
    
    <script>
        // Test fetch
        async function testFetch() {
            const testUrl = '/images/event_images/2023/AGM_2023_SACKOBA_Qatar/AGM_College_Day_Attendees_at_Tables.jpg';
            try {
                console.log('🔍 Testing fetch for:', testUrl);
                const response = await fetch(testUrl);
                console.log('📊 Fetch response:', {
                    status: response.status,
                    statusText: response.statusText,
                    url: response.url
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    console.log('✅ Fetch successful, blob size:', blob.size);
                } else {
                    console.error('❌ Fetch failed with status:', response.status);
                }
            } catch (error) {
                console.error('❌ Fetch error:', error);
            }
        }
        
        // Run test on page load
        window.onload = () => {
            testFetch();
        };
    </script>
</body>
</html>
